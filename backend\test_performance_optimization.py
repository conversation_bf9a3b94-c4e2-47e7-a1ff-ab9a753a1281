"""
性能优化测试
"""
import sys
import os
import time
import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor
import random

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_cache_manager():
    """测试缓存管理器"""
    print("=== 测试缓存管理器 ===")
    
    try:
        from core.cache_manager import CacheManager
        
        cache = CacheManager(max_size=100, default_ttl=10)
        
        # 测试基本操作
        print("测试基本缓存操作...")
        
        # 设置缓存
        cache.set("key1", "value1")
        cache.set("key2", {"data": "complex_value"})
        cache.set("key3", [1, 2, 3, 4, 5])
        
        # 获取缓存
        value1 = cache.get("key1")
        value2 = cache.get("key2")
        value3 = cache.get("key3")
        
        print(f"缓存测试结果:")
        print(f"  key1: {value1}")
        print(f"  key2: {value2}")
        print(f"  key3: {value3}")
        
        # 测试缓存统计
        stats = cache.get_stats()
        print(f"\n缓存统计:")
        print(f"  缓存大小: {stats['cache_size']}")
        print(f"  命中率: {stats['hit_rate']:.2%}")
        print(f"  总命中: {stats['total_hits']}")
        print(f"  总未命中: {stats['total_misses']}")
        
        # 测试缓存过期
        print("\n测试缓存过期...")
        cache.set("temp_key", "temp_value", ttl=1)
        print(f"设置临时缓存: {cache.get('temp_key')}")
        
        time.sleep(2)
        print(f"2秒后获取: {cache.get('temp_key')}")
        
        # 测试LRU淘汰
        print("\n测试LRU淘汰...")
        small_cache = CacheManager(max_size=3)
        
        for i in range(5):
            small_cache.set(f"key_{i}", f"value_{i}")
        
        final_stats = small_cache.get_stats()
        print(f"小缓存最终大小: {final_stats['cache_size']}")
        print(f"淘汰次数: {final_stats['total_evictions']}")
        
        return True
        
    except Exception as e:
        print(f"缓存管理器测试失败: {e}")
        return False

def test_performance_monitor():
    """测试性能监控"""
    print("\n=== 测试性能监控 ===")
    
    try:
        from core.cache_manager import PerformanceMonitor, monitor_performance
        
        monitor = PerformanceMonitor()
        
        # 模拟一些操作
        @monitor_performance("test_operation")
        def slow_operation():
            time.sleep(0.1)
            return "result"
        
        @monitor_performance("fast_operation")
        def fast_operation():
            time.sleep(0.01)
            return "result"
        
        # 执行操作
        print("执行性能测试操作...")
        
        for i in range(10):
            slow_operation()
            fast_operation()
        
        # 获取性能指标
        metrics = monitor.get_metrics()
        
        print("性能监控结果:")
        for operation, metric in metrics.items():
            print(f"  {operation}:")
            print(f"    执行次数: {metric['count']}")
            print(f"    平均时间: {metric['avg_time']:.4f}s")
            print(f"    最小时间: {metric['min_time']:.4f}s")
            print(f"    最大时间: {metric['max_time']:.4f}s")
        
        return len(metrics) > 0
        
    except Exception as e:
        print(f"性能监控测试失败: {e}")
        return False

def test_cache_decorator():
    """测试缓存装饰器"""
    print("\n=== 测试缓存装饰器 ===")
    
    try:
        from core.cache_manager import cache_result
        
        call_count = 0
        
        @cache_result(ttl=5)
        def expensive_function(x, y):
            nonlocal call_count
            call_count += 1
            time.sleep(0.1)  # 模拟耗时操作
            return x + y
        
        # 第一次调用
        start_time = time.time()
        result1 = expensive_function(1, 2)
        first_call_time = time.time() - start_time
        
        # 第二次调用（应该从缓存获取）
        start_time = time.time()
        result2 = expensive_function(1, 2)
        second_call_time = time.time() - start_time
        
        print(f"缓存装饰器测试:")
        print(f"  第一次调用结果: {result1}, 耗时: {first_call_time:.4f}s")
        print(f"  第二次调用结果: {result2}, 耗时: {second_call_time:.4f}s")
        print(f"  函数实际执行次数: {call_count}")
        print(f"  缓存加速比: {first_call_time / second_call_time:.2f}x")
        
        return result1 == result2 and call_count == 1 and second_call_time < first_call_time
        
    except Exception as e:
        print(f"缓存装饰器测试失败: {e}")
        return False

def test_concurrent_cache_access():
    """测试并发缓存访问"""
    print("\n=== 测试并发缓存访问 ===")
    
    try:
        from core.cache_manager import CacheManager
        
        cache = CacheManager(max_size=1000)
        
        def worker(worker_id):
            """工作线程"""
            for i in range(100):
                key = f"worker_{worker_id}_key_{i}"
                value = f"worker_{worker_id}_value_{i}"
                
                # 设置缓存
                cache.set(key, value)
                
                # 随机获取一些缓存
                if random.random() > 0.5:
                    cache.get(key)
                
                # 随机删除一些缓存
                if random.random() > 0.9:
                    cache.delete(key)
        
        # 启动多个线程
        print("启动10个并发线程...")
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(worker, i) for i in range(10)]
            
            # 等待所有线程完成
            for future in futures:
                future.result()
        
        end_time = time.time()
        
        # 获取最终统计
        final_stats = cache.get_stats()
        
        print(f"并发测试结果:")
        print(f"  总耗时: {end_time - start_time:.2f}s")
        print(f"  最终缓存大小: {final_stats['cache_size']}")
        print(f"  总操作数: {final_stats['total_sets'] + final_stats['total_hits'] + final_stats['total_misses']}")
        print(f"  命中率: {final_stats['hit_rate']:.2%}")
        
        return True
        
    except Exception as e:
        print(f"并发缓存测试失败: {e}")
        return False

def test_system_metrics():
    """测试系统指标获取"""
    print("\n=== 测试系统指标获取 ===")
    
    try:
        from app.services.performance_service import PerformanceService
        
        service = PerformanceService()
        
        # 获取系统指标
        system_metrics = service.get_system_metrics()
        
        if 'error' in system_metrics:
            print(f"获取系统指标失败: {system_metrics['error']}")
            return False
        
        print("系统性能指标:")
        print(f"  CPU使用率: {system_metrics['cpu']['usage_percent']:.1f}%")
        print(f"  CPU核心数: {system_metrics['cpu']['core_count']}")
        print(f"  内存使用率: {system_metrics['memory']['usage_percent']:.1f}%")
        print(f"  可用内存: {system_metrics['memory']['available_gb']:.2f} GB")
        print(f"  磁盘使用率: {system_metrics['disk']['usage_percent']:.1f}%")
        print(f"  磁盘可用空间: {system_metrics['disk']['free_gb']:.2f} GB")
        
        # 获取缓存指标
        cache_metrics = service.get_cache_metrics()
        
        if 'error' not in cache_metrics:
            print(f"\n缓存性能指标:")
            print(f"  命中率: {cache_metrics['efficiency']['hit_rate']:.2%}")
            print(f"  效率评分: {cache_metrics['efficiency']['efficiency_score']:.1f}")
            print(f"  缓存利用率: {cache_metrics['memory_usage']['utilization']:.2%}")
        
        # 获取应用指标
        app_metrics = service.get_application_metrics()
        
        if 'error' not in app_metrics:
            print(f"\n应用性能指标:")
            print(f"  总操作数: {app_metrics['summary']['total_operations']}")
            print(f"  平均响应时间: {app_metrics['summary']['avg_response_time']:.4f}s")
            print(f"  性能评分: {app_metrics['performance_score']:.1f}")
        
        return True
        
    except Exception as e:
        print(f"系统指标测试失败: {e}")
        return False

def test_performance_optimization():
    """测试性能优化"""
    print("\n=== 测试性能优化 ===")
    
    try:
        from app.services.performance_service import PerformanceService
        
        service = PerformanceService()
        
        # 生成一些缓存数据
        cache_manager = service.cache_manager
        for i in range(50):
            cache_manager.set(f"test_key_{i}", f"test_value_{i}")
        
        # 模拟一些缓存访问
        for i in range(100):
            key = f"test_key_{random.randint(0, 49)}"
            cache_manager.get(key)
        
        # 执行缓存优化
        print("执行缓存优化...")
        optimization_result = service.optimize_cache_settings()
        
        if optimization_result['success']:
            print(f"缓存优化结果:")
            print(f"  应用的优化数量: {optimization_result['optimizations_applied']}")
            
            for rec in optimization_result['recommendations']:
                print(f"  - {rec['type']}: {rec.get('reason', 'N/A')}")
        
        # 生成性能报告
        print("\n生成性能报告...")
        performance_report = service.get_performance_report()
        
        if 'error' not in performance_report:
            print(f"性能报告:")
            print(f"  总体评分: {performance_report['overall_score']:.1f}")
            print(f"  健康状态: {performance_report['health_status']}")
            print(f"  建议数量: {len(performance_report['recommendations'])}")
            
            for rec in performance_report['recommendations'][:3]:  # 显示前3个建议
                print(f"  - {rec}")
        
        return optimization_result['success']
        
    except Exception as e:
        print(f"性能优化测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始系统性能优化测试")
    print("=" * 60)
    
    tests = [
        ("缓存管理器", test_cache_manager),
        ("性能监控", test_performance_monitor),
        ("缓存装饰器", test_cache_decorator),
        ("并发缓存访问", test_concurrent_cache_access),
        ("系统指标获取", test_system_metrics),
        ("性能优化", test_performance_optimization)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"✅ {test_name} 测试完成")
        except Exception as e:
            print(f"❌ {test_name} 测试失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count >= 5:
        print("\n🎉 系统性能优化测试通过！")
        print("💡 核心功能:")
        print("- 智能缓存管理 ✅")
        print("- 性能实时监控 ✅")
        print("- 并发安全访问 ✅")
        print("- 自动性能优化 ✅")
        print("- 系统指标收集 ✅")
        print("- 性能报告生成 ✅")
    else:
        print("\n⚠️  部分测试失败，请检查性能优化模块")

if __name__ == "__main__":
    main()
